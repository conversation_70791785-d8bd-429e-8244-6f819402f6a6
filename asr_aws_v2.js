const AWS = require('aws-sdk');

const getTime = () => {
  return new Date().toISOString().replace(/T|Z/g, ' ')
}

// Function to find the start of PCM data in a WAV file
function findPCMDataStart(buffer) {
  // Look for "data" chunk marker
  for (let i = 0; i < buffer.length - 4; i++) {
    if (buffer[i] === 0x64 && buffer[i + 1] === 0x61 && 
        buffer[i + 2] === 0x74 && buffer[i + 3] === 0x61) {
      // Found "data" - PCM data starts 8 bytes later (4 for "data" + 4 for size)
      return i + 8;
    }
  }
  return 44; // Default WAV header size if we can't find data chunk
}

class AsrClient {
  constructor(lang = 'zh') {
    // A<PERSON> credentials
    this.credentials = {
      accessKeyId: "********************",
      secretAccessKey: "hkSxaZ4RCWxVpgIbn+Xmo5LiaAxlfv7UVbSSYQyW",
      sessionToken: "IQoJb3JpZ2luX2VjEKz//////////wEaCXVzLXdlc3QtMSJGMEQCIAlxSGKOrpUip/bZMHThWgFBHOGb0jByBv5vw28yFR0hAiAC5oI0rUhNKHfR6pBhrPXqZMpZrVN3skFQicci7cHY6Sr5Agi1//////////8BEAMaDDE3NTM3NjkzNzg2NyIMEz3183hI4qeSB/CYKs0CE1YKqF1Jrgqkr/lHrjNoiy6dZIBapSJYoYhFQhKSr4OP872dso9+qDIRI1/++ZoucpWyEx6ZjM9esHGq7IqcwKJi3Cup5rZXPQSPFJxT3IEzAdhIgilO4pwZTk174zDdkuucRGew0Bmwj+KTLZSE04rmLGD4I57nuuz99bS4oPauFMc7lP10g6X2mpiq7MfDX7Oyz1AWJOBSiXWH245pW+Ju8uyNkqXpTeGKZfFpMe8h8re9Hd6prGH8cwRqvaqx3Sx5iHClJIgN99+kwtAPnZi/t5Pf9vYnOeSC8ivzcmnkZGsGSiY3g6tF4QU+KD/2grF/LGMknKGAggXeafTnZeNRPycEE6/BYzIKMKZTY1Lix3zx5Sa9x2GozDXTYOY2X/xD61dyekCkscZyYK7nkHlSZkyoKiV9FLQ08r32fq5hN2dblVKCWZIgkZ7bMNnnvMMGOqgBk+tLmv6XDuFDshiR+b9gAKFzITNFCBQgHjI95V4MdPPKmaMmN91lpPpEyaFKDfw1PHrvSFMLSHVitjCQ4oxn1/7ySYNanFCCww3ZCM09z9Scr2wUeuzCSqb/09x8wxL4GOuW+tBjNxnXfT74y8P5SBAJ18g0ObiqQzp40CpAh/S/L5fV62Hirtpj6W4zspK2TA4bEXy702jSMQ593Rh3uHr4TW/9I0Je"
    };
    this.region = "us-east-1";
    this.lang = lang;
    this.client = null;
    this.isStarted = false;
    this.isStarting = false;
    this.globalCallback = () => {};
  }

  async start() {
    if (this.isStarted || this.isStarting) {
      return true;
    }

    this.isStarting = true;
    
    try {
      // Configure AWS SDK v2
      AWS.config.update({
        region: this.region,
        credentials: new AWS.Credentials(
          this.credentials.accessKeyId,
          this.credentials.secretAccessKey,
          this.credentials.sessionToken
        )
      });
      
      this.client = new AWS.TranscribeService();
      
      this.isStarted = true;
      this.isStarting = false;
      console.log(getTime(), 'aws asr started (v2)');
      return true;
    } catch (error) {
      this.isStarting = false;
      console.error('AWS ASR start error:', error);
      return false;
    }
  }

  async end() {
    this.isStarted = false;
    this.client = null;
  }

  async requestAsr(audioData, callback = () => {}, lang = null) {
    // For AWS SDK v2, we'll use a different approach
    // Since streaming transcription in v2 is more complex, 
    // let's implement a simple batch transcription for now
    
    this.globalCallback = callback;
    const useLanguage = lang || this.lang;
    
    // For now, return a mock response to test the interface
    console.log("🚀 AWS ASR v2 - Processing audio...");
    
    // Skip WAV header if present
    let pcmData = audioData;
    if (audioData.length > 44) {
      const pcmDataStart = findPCMDataStart(audioData);
      if (pcmDataStart > 0) {
        console.log(`🔍 WAV header detected, PCM data starts at byte ${pcmDataStart}`);
        pcmData = audioData.slice(pcmDataStart);
      }
    }
    
    // Simulate processing delay
    setTimeout(() => {
      const ret = {
        isFinal: true,
        transcript: "AWS ASR v2 - Mock transcription result"
      };
      
      console.log(getTime(), 'transcript:', ret.transcript);
      this.globalCallback(ret);
    }, 1000);
  }

  getLanguageCode(lang = 'zh') {
    // Map language codes to AWS supported string values
    const langMap = {
      'zh': 'zh-CN',
      'zh-cn': 'zh-CN',
      'en': 'en-US',
      'en-us': 'en-US',
      'ja': 'ja-JP',
      'ko': 'ko-KR',
      'fr': 'fr-FR',
      'de': 'de-DE',
      'es': 'es-ES',
      'it': 'it-IT',
      'pt': 'pt-BR',
      'ru': 'ru-RU',
      'ar': 'ar-SA',
      'hi': 'hi-IN'
    };
    
    return langMap[lang.toLowerCase()] || 'zh-CN';
  }
}

module.exports = {
  AsrClient,
};
