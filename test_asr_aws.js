const { AsrClient } = require('./asr_aws');
const fs = require('fs');

// 示例AWS凭证配置 - 请替换为您的实际凭证
const credentials = {
  accessKeyId: "********************",
  secretAccessKey: "hkSxaZ4RCWxVpgIbn+Xmo5LiaAxlfv7UVbSSYQyW",
  sessionToken: "IQoJb3JpZ2luX2VjEKz//////////wEaCXVzLXdlc3QtMSJGMEQCIAlxSGKOrpUip/bZMHThWgFBHOGb0jByBv5vw28yFR0hAiAC5oI0rUhNKHfR6pBhrPXqZMpZrVN3skFQicci7cHY6Sr5Agi1//////////8BEAMaDDE3NTM3NjkzNzg2NyIMEz3183hI4qeSB/CYKs0CE1YKqF1Jrgqkr/lHrjNoiy6dZIBapSJYoYhFQhKSr4OP872dso9+qDIRI1/++ZoucpWyEx6ZjM9esHGq7IqcwKJi3Cup5rZXPQSPFJxT3IEzAdhIgilO4pwZTk174zDdkuucRGew0Bmwj+KTLZSE04rmLGD4I57nuuz99bS4oPauFMc7lP10g6X2mpiq7MfDX7Oyz1AWJOBSiXWH245pW+Ju8uyNkqXpTeGKZfFpMe8h8re9Hd6prGH8cwRqvaqx3Sx5iHClJIgN99+kwtAPnZi/t5Pf9vYnOeSC8ivzcmnkZGsGSiY3g6tF4QU+KD/2grF/LGMknKGAggXeafTnZeNRPycEE6/BYzIKMKZTY1Lix3zx5Sa9x2GozDXTYOY2X/xD61dyekCkscZyYK7nkHlSZkyoKiV9FLQ08r32fq5hN2dblVKCWZIgkZ7bMNnnvMMGOqgBk+tLmv6XDuFDshiR+b9gAKFzITNFCBQgHjI95V4MdPPKmaMmN91lpPpEyaFKDfw1PHrvSFMLSHVitjCQ4oxn1/7ySYNanFCCww3ZCM09z9Scr2wUeuzCSqb/09x8wxL4GOuW+tBjNxnXfT74y8P5SBAJ18g0ObiqQzp40CpAh/S/L5fV62Hirtpj6W4zspK2TA4bEXy702jSMQ593Rh3uHr4TW/9I0Je"
};

async function testAsrAws() {
  console.log('Testing AWS ASR Client...');
  
  // 创建ASR客户端实例
  const asrClient = new AsrClient(credentials, "us-east-1");
  
  try {
    // 读取音频文件
    const audioFile = './history_cn.wav'; // 请确保文件存在
    if (!fs.existsSync(audioFile)) {
      console.error('Audio file not found:', audioFile);
      return;
    }
    
    const audioData = fs.readFileSync(audioFile);
    console.log('Audio file loaded, size:', audioData.length, 'bytes');
    
    // 定义回调函数处理识别结果
    const callback = (result) => {
      console.log('Recognition result:', {
        isFinal: result.isFinal,
        transcript: result.transcript
      });
    };
    
    // 开始语音识别
    console.log('Starting ASR...');
    await asrClient.requestAsr(audioData, callback, 'zh');
    
    // 等待一段时间让识别完成
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // 结束ASR
    await asrClient.end();
    console.log('ASR test completed');
    
  } catch (error) {
    console.error('Test error:', error);
  }
}

// 运行测试
testAsrAws().catch(console.error);
