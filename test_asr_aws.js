const { AsrClient } = require('./asr_aws');
const fs = require('fs');

// 示例AWS凭证配置 - 请替换为您的实际凭证
const credentials = {
  accessKeyId: "YOUR_ACCESS_KEY_ID",
  secretAccessKey: "YOUR_SECRET_ACCESS_KEY",
  sessionToken: "YOUR_SESSION_TOKEN" // 如果使用临时凭证
};

async function testAsrAws() {
  console.log('Testing AWS ASR Client...');
  
  // 创建ASR客户端实例
  const asrClient = new AsrClient(credentials, "us-east-1");
  
  try {
    // 读取音频文件
    const audioFile = './history_cn.wav'; // 请确保文件存在
    if (!fs.existsSync(audioFile)) {
      console.error('Audio file not found:', audioFile);
      return;
    }
    
    const audioData = fs.readFileSync(audioFile);
    console.log('Audio file loaded, size:', audioData.length, 'bytes');
    
    // 定义回调函数处理识别结果
    const callback = (result) => {
      console.log('Recognition result:', {
        isFinal: result.isFinal,
        transcript: result.transcript
      });
    };
    
    // 开始语音识别
    console.log('Starting ASR...');
    await asrClient.requestAsr(audioData, callback, 'zh');
    
    // 等待一段时间让识别完成
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // 结束ASR
    await asrClient.end();
    console.log('ASR test completed');
    
  } catch (error) {
    console.error('Test error:', error);
  }
}

// 运行测试
testAsrAws().catch(console.error);
