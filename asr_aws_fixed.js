const {
  TranscribeStreamingClient,
  StartStreamTranscriptionCommand,
} = require("@aws-sdk/client-transcribe-streaming");

const getTime = () => {
  return new Date().toISOString().replace(/T|Z/g, ' ')
}

// Function to find the start of PCM data in a WAV file
function findPCMDataStart(buffer) {
  // Look for "data" chunk marker
  for (let i = 0; i < buffer.length - 4; i++) {
    if (buffer[i] === 0x64 && buffer[i + 1] === 0x61 && 
        buffer[i + 2] === 0x74 && buffer[i + 3] === 0x61) {
      // Found "data" - PCM data starts 8 bytes later (4 for "data" + 4 for size)
      return i + 8;
    }
  }
  return 44; // Default WAV header size if we can't find data chunk
}

class AsrClient {
  constructor(lang = 'zh') {
    // AWS credentials - you should set these via environment variables or config
    this.credentials = {
      accessKeyId: "********************",
      secretAccessKey: "hkSxaZ4RCWxVpgIbn+Xmo5LiaAxlfv7UVbSSYQyW",
      sessionToken: "IQoJb3JpZ2luX2VjEKz//////////wEaCXVzLXdlc3QtMSJGMEQCIAlxSGKOrpUip/bZMHThWgFBHOGb0jByBv5vw28yFR0hAiAC5oI0rUhNKHfR6pBhrPXqZMpZrVN3skFQicci7cHY6Sr5Agi1//////////8BEAMaDDE3NTM3NjkzNzg2NyIMEz3183hI4qeSB/CYKs0CE1YKqF1Jrgqkr/lHrjNoiy6dZIBapSJYoYhFQhKSr4OP872dso9+qDIRI1/++ZoucpWyEx6ZjM9esHGq7IqcwKJi3Cup5rZXPQSPFJxT3IEzAdhIgilO4pwZTk174zDdkuucRGew0Bmwj+KTLZSE04rmLGD4I57nuuz99bS4oPauFMc7lP10g6X2mpiq7MfDX7Oyz1AWJOBSiXWH245pW+Ju8uyNkqXpTeGKZfFpMe8h8re9Hd6prGH8cwRqvaqx3Sx5iHClJIgN99+kwtAPnZi/t5Pf9vYnOeSC8ivzcmnkZGsGSiY3g6tF4QU+KD/2grF/LGMknKGAggXeafTnZeNRPycEE6/BYzIKMKZTY1Lix3zx5Sa9x2GozDXTYOY2X/xD61dyekCkscZyYK7nkHlSZkyoKiV9FLQ08r32fq5hN2dblVKCWZIgkZ7bMNnnvMMGOqgBk+tLmv6XDuFDshiR+b9gAKFzITNFCBQgHjI95V4MdPPKmaMmN91lpPpEyaFKDfw1PHrvSFMLSHVitjCQ4oxn1/7ySYNanFCCww3ZCM09z9Scr2wUeuzCSqb/09x8wxL4GOuW+tBjNxnXfT74y8P5SBAJ18g0ObiqQzp40CpAh/S/L5fV62Hirtpj6W4zspK2TA4bEXy702jSMQ593Rh3uHr4TW/9I0Je"
    };
    this.region = "us-east-1";
    this.lang = lang;
    this.client = null;
    this.isStarted = false;
    this.isStarting = false;
    this.globalCallback = () => {};
  }

  async start() {
    if (this.isStarted || this.isStarting) {
      return true;
    }

    this.isStarting = true;
    
    try {
      this.client = new TranscribeStreamingClient({
        region: this.region,
        credentials: this.credentials,
        maxAttempts: 1,
      });
      
      this.isStarted = true;
      this.isStarting = false;
      console.log(getTime(), 'aws asr started');
      return true;
    } catch (error) {
      this.isStarting = false;
      console.error('AWS ASR start error:', error);
      return false;
    }
  }

  async end() {
    this.isStarted = false;
    this.client = null;
  }

  async requestAsr(audioData, callback = () => {}, lang = null) {
    if (!this.isStarted && !this.isStarting) {
      await this.start();
    }

    if (this.isStarting) {
      // Wait for start to complete
      await new Promise(resolve => {
        const checkInterval = setInterval(() => {
          if (!this.isStarting) {
            clearInterval(checkInterval);
            resolve();
          }
        }, 10);
      });
    }

    this.globalCallback = callback;
    
    // Use constructor language if not provided
    const useLanguage = lang || this.lang;
    
    // Process audio data directly (like original demo)
    await this.transcribeAudio(audioData, useLanguage);
  }

  async transcribeAudio(audioData, lang = 'zh') {
    if (!this.client) {
      return;
    }

    try {
      console.log("🚀 Starting AWS Transcribe Streaming...");
      
      // Skip WAV header if present
      let pcmData = audioData;
      if (audioData.length > 44) {
        const pcmDataStart = findPCMDataStart(audioData);
        if (pcmDataStart > 0) {
          console.log(`🔍 WAV header detected, PCM data starts at byte ${pcmDataStart}`);
          pcmData = audioData.slice(pcmDataStart);
        }
      }

      // Create audio stream exactly like original demo
      const audioStream = async function* () {
        let chunkCount = 0;
        let totalBytes = 0;
        const maxChunkSize = 1024 * 4; // 4KB max chunks

        // Process the PCM data in chunks
        for (let i = 0; i < pcmData.length; i += maxChunkSize) {
          const smallChunk = pcmData.slice(i, i + maxChunkSize);
          chunkCount++;
          totalBytes += smallChunk.length;

          console.log(`📦 Chunk ${chunkCount}: ${smallChunk.length} bytes PCM data`);
          yield { AudioEvent: { AudioChunk: smallChunk } };

          // Small delay between chunks
          await new Promise(resolve => setTimeout(resolve, 50));
        }

        console.log(`✅ Audio stream complete: ${chunkCount} chunks, ${totalBytes} bytes PCM data`);
      };

      // Create parameters object with explicit property assignment to avoid SDK issues
      const languageCode = this.getLanguageCode(lang);
      const mediaEncoding = "pcm";
      const sampleRate = 16000;
      
      // Try creating the parameters object in a way that avoids the $source issue
      const params = {};
      params.LanguageCode = languageCode;
      params.MediaEncoding = mediaEncoding;
      params.MediaSampleRateHertz = sampleRate;
      params.AudioStream = audioStream();

      console.log("📡 Sending transcription request...");
      console.log("Parameters:", {
        LanguageCode: params.LanguageCode,
        MediaEncoding: params.MediaEncoding,
        MediaSampleRateHertz: params.MediaSampleRateHertz
      });
      
      const command = new StartStreamTranscriptionCommand(params);
      const response = await this.client.send(command);
      
      console.log("🎧 Listening for transcription results...");
      let hasTranscript = false;
      
      for await (const event of response.TranscriptResultStream) {
        if (event.TranscriptEvent) {
          const results = event.TranscriptEvent.Transcript.Results;
          
          if (results && results.length > 0) {
            results.forEach((result) => {
              if (result.Alternatives && result.Alternatives.length > 0) {
                const transcript = result.Alternatives[0].Transcript;
                const confidence = result.Alternatives[0].Confidence;
                const isFinal = !result.IsPartial;
                
                if (transcript && transcript.trim()) {
                  hasTranscript = true;
                  const ret = {
                    isFinal: isFinal,
                    transcript: transcript.trim()
                  };
                  
                  console.log(`📝 ${isFinal ? '[FINAL]' : '[PARTIAL]'} "${transcript}" (confidence: ${confidence || 'N/A'})`);
                  this.globalCallback(ret);
                }
              }
            });
          }
        }
      }
      
      if (hasTranscript) {
        console.log("🎉 Transcription completed successfully!");
      } else {
        console.log("⚠️  No speech detected in the audio file.");
        console.log("   This could mean the audio is silent, too quiet, or unclear.");
      }
      
    } catch (err) {
      console.error("❌ Error during transcription:");
      console.error(`   ${err.name}: ${err.message}`);
      console.error("Stack trace:", err.stack);
    }
  }

  getLanguageCode(lang = 'zh') {
    // Map language codes to AWS supported string values
    const langMap = {
      'zh': 'zh-CN',
      'zh-cn': 'zh-CN',
      'en': 'en-US',
      'en-us': 'en-US',
      'ja': 'ja-JP',
      'ko': 'ko-KR',
      'fr': 'fr-FR',
      'de': 'de-DE',
      'es': 'es-ES',
      'it': 'it-IT',
      'pt': 'pt-BR',
      'ru': 'ru-RU',
      'ar': 'ar-SA',
      'hi': 'hi-IN'
    };
    
    return langMap[lang.toLowerCase()] || 'zh-CN';
  }
}

module.exports = {
  AsrClient,
};
