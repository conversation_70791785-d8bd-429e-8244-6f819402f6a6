const {
  TranscribeStreamingClient,
  StartStreamTranscriptionCommand,
} = require("@aws-sdk/client-transcribe-streaming");
const { createReadStream } = require("fs");
const { join } = require("path");

const LanguageCode = "zh-CN";
const MediaEncoding = "pcm";
const MediaSampleRateHertz = 16000;

// Your credentials with session token
const credentials = {
  accessKeyId: "********************",
  secretAccessKey: "hkSxaZ4RCWxVpgIbn+Xmo5LiaAxlfv7UVbSSYQyW",
  sessionToken: "IQoJb3JpZ2luX2VjEKz//////////wEaCXVzLXdlc3QtMSJGMEQCIAlxSGKOrpUip/bZMHThWgFBHOGb0jByBv5vw28yFR0hAiAC5oI0rUhNKHfR6pBhrPXqZMpZrVN3skFQicci7cHY6Sr5Agi1//////////8BEAMaDDE3NTM3NjkzNzg2NyIMEz3183hI4qeSB/CYKs0CE1YKqF1Jrgqkr/lHrjNoiy6dZIBapSJYoYhFQhKSr4OP872dso9+qDIRI1/++ZoucpWyEx6ZjM9esHGq7IqcwKJi3Cup5rZXPQSPFJxT3IEzAdhIgilO4pwZTk174zDdkuucRGew0Bmwj+KTLZSE04rmLGD4I57nuuz99bS4oPauFMc7lP10g6X2mpiq7MfDX7Oyz1AWJOBSiXWH245pW+Ju8uyNkqXpTeGKZfFpMe8h8re9Hd6prGH8cwRqvaqx3Sx5iHClJIgN99+kwtAPnZi/t5Pf9vYnOeSC8ivzcmnkZGsGSiY3g6tF4QU+KD/2grF/LGMknKGAggXeafTnZeNRPycEE6/BYzIKMKZTY1Lix3zx5Sa9x2GozDXTYOY2X/xD61dyekCkscZyYK7nkHlSZkyoKiV9FLQ08r32fq5hN2dblVKCWZIgkZ7bMNnnvMMGOqgBk+tLmv6XDuFDshiR+b9gAKFzITNFCBQgHjI95V4MdPPKmaMmN91lpPpEyaFKDfw1PHrvSFMLSHVitjCQ4oxn1/7ySYNanFCCww3ZCM09z9Scr2wUeuzCSqb/09x8wxL4GOuW+tBjNxnXfT74y8P5SBAJ18g0ObiqQzp40CpAh/S/L5fV62Hirtpj6W4zspK2TA4bEXy702jSMQ593Rh3uHr4TW/9I0Je"
};

// Function to find the start of PCM data in a WAV file
function findPCMDataStart(buffer) {
  // Look for "data" chunk marker
  for (let i = 0; i < buffer.length - 4; i++) {
    if (buffer[i] === 0x64 && buffer[i + 1] === 0x61 && 
        buffer[i + 2] === 0x74 && buffer[i + 3] === 0x61) {
      // Found "data" - PCM data starts 8 bytes later (4 for "data" + 4 for size)
      return i + 8;
    }
  }
  return 44; // Default WAV header size if we can't find data chunk
}

async function startRequest() {
  console.log("🚀 Starting AWS Transcribe Streaming (Fixed for WAV files)...");
  
  try {
    const client = new TranscribeStreamingClient({
      region: "us-east-1",
      credentials,
      maxAttempts: 1,
    });

    // Create audio stream that skips WAV header and sends small chunks
    const audioStream = async function* () {
      const audio = createReadStream(join(__dirname, "history_cn.wav"));

      let chunkCount = 0;
      let totalBytes = 0;
      let headerSkipped = false;
      let pcmDataStart = 0;
      const maxChunkSize = 1024 * 4; // 4KB max chunks

      for await (const chunk of audio) {
        let dataToProcess;

        if (!headerSkipped) {
          // Find where PCM data starts in the first chunk
          pcmDataStart = findPCMDataStart(chunk);
          console.log(`🔍 WAV header detected, PCM data starts at byte ${pcmDataStart}`);

          // Get only the PCM data part
          dataToProcess = chunk.slice(pcmDataStart);
          headerSkipped = true;
        } else {
          // All subsequent chunks are pure PCM data
          dataToProcess = chunk;
        }

        // Break large chunks into smaller pieces
        for (let i = 0; i < dataToProcess.length; i += maxChunkSize) {
          const smallChunk = dataToProcess.slice(i, i + maxChunkSize);
          chunkCount++;
          totalBytes += smallChunk.length;

          console.log(`📦 Chunk ${chunkCount}: ${smallChunk.length} bytes PCM data`);
          yield { AudioEvent: { AudioChunk: smallChunk } };

          // Small delay between chunks
          await new Promise(resolve => setTimeout(resolve, 50));
        }
      }

      console.log(`✅ Audio stream complete: ${chunkCount} chunks, ${totalBytes} bytes PCM data`);
    };

    const params = {
      LanguageCode,
      MediaEncoding,
      MediaSampleRateHertz,
      AudioStream: audioStream(),
    };

    console.log("📡 Sending transcription request...");
    const command = new StartStreamTranscriptionCommand(params);
    const response = await client.send(command);
    
    console.log("🎧 Listening for transcription results...");
    let hasTranscript = false;
    
    for await (const event of response.TranscriptResultStream) {
      if (event.TranscriptEvent) {
        const results = event.TranscriptEvent.Transcript.Results;
        
        if (results && results.length > 0) {
          results.forEach((result) => {
            if (result.Alternatives && result.Alternatives.length > 0) {
              const transcript = result.Alternatives[0].Transcript;
              const confidence = result.Alternatives[0].Confidence;
              const isFinal = !result.IsPartial;
              
              if (transcript && transcript.trim()) {
                hasTranscript = true;
                console.log(`📝 ${isFinal ? '[FINAL]' : '[PARTIAL]'} "${transcript}" (confidence: ${confidence || 'N/A'})`);
              }
            }
          });
        }
      }
    }
    
    if (hasTranscript) {
      console.log("🎉 Transcription completed successfully!");
    } else {
      console.log("⚠️  No speech detected in the audio file.");
      console.log("   This could mean the audio is silent, too quiet, or unclear.");
    }
    
  } catch (err) {
    console.error("❌ Error during transcription:");
    console.error(`   ${err.name}: ${err.message}`);
  }
}

startRequest().catch(console.error);
