# AWS ASR Client 使用说明

## 概述

`asr_aws.js` 是一个基于 AWS Transcribe Streaming 的语音识别客户端，提供与 `asr_ali.js` 和 `asr_keda.js` 相同的接口格式。

## 安装依赖

```bash
npm install @aws-sdk/client-transcribe-streaming
```

## 使用方法

### 1. 导入模块

```javascript
const { AsrClient } = require('./asr_aws');
```

### 2. 创建客户端实例

```javascript
// AWS 凭证配置
const credentials = {
  accessKeyId: "YOUR_ACCESS_KEY_ID",
  secretAccessKey: "YOUR_SECRET_ACCESS_KEY",
  sessionToken: "YOUR_SESSION_TOKEN" // 可选，用于临时凭证
};

// 创建客户端
const asrClient = new AsrClient(credentials, "us-east-1");
```

### 3. 语音识别

```javascript
const fs = require('fs');

// 读取音频文件
const audioData = fs.readFileSync('./audio.wav');

// 定义回调函数
const callback = (result) => {
  console.log('识别结果:', {
    isFinal: result.isFinal,    // 是否为最终结果
    transcript: result.transcript // 识别文本
  });
};

// 开始识别
await asrClient.requestAsr(audioData, callback, 'zh');

// 结束识别
await asrClient.end();
```

## API 接口

### AsrClient 类

#### 构造函数
```javascript
new AsrClient(credentials, region)
```

- `credentials`: AWS 凭证对象
- `region`: AWS 区域，默认为 "us-east-1"

#### 方法

##### start()
启动 ASR 服务
```javascript
await asrClient.start();
```

##### requestAsr(audioData, callback, lang)
请求语音识别
- `audioData`: 音频数据 (Buffer)
- `callback`: 回调函数，接收识别结果
- `lang`: 语言代码，默认为 'zh'

##### end()
结束 ASR 服务
```javascript
await asrClient.end();
```

## 支持的语言

- `zh` / `zh-cn`: 中文 (zh-CN)
- `en` / `en-us`: 英语 (en-US)
- `ja`: 日语 (ja-JP)
- `ko`: 韩语 (ko-KR)
- `fr`: 法语 (fr-FR)
- `de`: 德语 (de-DE)
- `es`: 西班牙语 (es-ES)
- `it`: 意大利语 (it-IT)
- `pt`: 葡萄牙语 (pt-BR)
- `ru`: 俄语 (ru-RU)
- `ar`: 阿拉伯语 (ar-SA)
- `hi`: 印地语 (hi-IN)

## 音频格式要求

- 格式: WAV 或 PCM
- 采样率: 16000 Hz
- 编码: 16-bit PCM
- 声道: 单声道

## 注意事项

1. 需要有效的 AWS 凭证和适当的权限
2. 音频文件会自动跳过 WAV 头部，提取 PCM 数据
3. 大音频文件会被分割成小块进行处理
4. 识别结果通过回调函数异步返回

## 错误处理

客户端会自动处理常见错误，错误信息会输出到控制台。建议在生产环境中添加适当的错误处理逻辑。

## 示例

参见 `test_asr_aws.js` 文件获取完整的使用示例。
