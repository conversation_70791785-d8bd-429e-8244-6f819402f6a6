const {
  TranscribeStreamingClient,
  StartStreamTranscriptionCommand,
} = require("@aws-sdk/client-transcribe-streaming");

const getTime = () => {
  return new Date().toISOString().replace(/T|Z/g, ' ')
}

// Function to find the start of PCM data in a WAV file
function findPCMDataStart(buffer) {
  // Look for "data" chunk marker
  for (let i = 0; i < buffer.length - 4; i++) {
    if (buffer[i] === 0x64 && buffer[i + 1] === 0x61 && 
        buffer[i + 2] === 0x74 && buffer[i + 3] === 0x61) {
      // Found "data" - PCM data starts 8 bytes later (4 for "data" + 4 for size)
      return i + 8;
    }
  }
  return 44; // Default WAV header size if we can't find data chunk
}

class AsrClient {
  constructor(credentials = null, region = "us-east-1") {
    this.credentials = credentials;
    this.region = region;
    this.client = null;
    this.isStarted = false;
    this.isStarting = false;
    this.globalCallback = () => {};
  }

  async start() {
    if (this.isStarted || this.isStarting) {
      return true;
    }

    this.isStarting = true;
    
    try {
      this.client = new TranscribeStreamingClient({
        region: this.region,
        credentials: this.credentials,
        maxAttempts: 1,
      });
      
      this.isStarted = true;
      this.isStarting = false;
      console.log(getTime(), 'aws asr started');
      return true;
    } catch (error) {
      this.isStarting = false;
      console.error('AWS ASR start error:', error);
      return false;
    }
  }

  async end() {
    this.isStarted = false;
    this.client = null;
  }

  async requestAsr(audioData, callback = () => {}, lang = 'zh') {
    if (!this.isStarted && !this.isStarting) {
      await this.start();
    }

    if (this.isStarting) {
      // Wait for start to complete
      await new Promise(resolve => {
        const checkInterval = setInterval(() => {
          if (!this.isStarting) {
            clearInterval(checkInterval);
            resolve();
          }
        }, 10);
      });
    }

    this.globalCallback = callback;
    
    // Process audio data directly (like original demo)
    await this.transcribeAudio(audioData, lang);
  }

  async transcribeAudio(audioData, lang = 'zh') {
    if (!this.client) {
      return;
    }

    try {
      console.log("🚀 Starting AWS Transcribe Streaming...");
      
      // Skip WAV header if present
      let pcmData = audioData;
      if (audioData.length > 44) {
        const pcmDataStart = findPCMDataStart(audioData);
        if (pcmDataStart > 0) {
          console.log(`🔍 WAV header detected, PCM data starts at byte ${pcmDataStart}`);
          pcmData = audioData.slice(pcmDataStart);
        }
      }

      // Create audio stream exactly like original demo
      const audioStream = async function* () {
        let chunkCount = 0;
        let totalBytes = 0;
        const maxChunkSize = 1024 * 4; // 4KB max chunks

        // Process the PCM data in chunks
        for (let i = 0; i < pcmData.length; i += maxChunkSize) {
          const smallChunk = pcmData.slice(i, i + maxChunkSize);
          chunkCount++;
          totalBytes += smallChunk.length;

          console.log(`📦 Chunk ${chunkCount}: ${smallChunk.length} bytes PCM data`);
          yield { AudioEvent: { AudioChunk: smallChunk } };

          // Small delay between chunks
          await new Promise(resolve => setTimeout(resolve, 50));
        }

        console.log(`✅ Audio stream complete: ${chunkCount} chunks, ${totalBytes} bytes PCM data`);
      };

      // Use exact same parameters as original demo
      const LanguageCode = this.getLanguageCode(lang);
      const MediaEncoding = "pcm";
      const MediaSampleRateHertz = 16000;

      const params = {
        LanguageCode,
        MediaEncoding,
        MediaSampleRateHertz,
        AudioStream: audioStream(),
      };

      console.log("📡 Sending transcription request...");
      const command = new StartStreamTranscriptionCommand(params);
      const response = await this.client.send(command);
      
      console.log("🎧 Listening for transcription results...");
      let hasTranscript = false;
      
      for await (const event of response.TranscriptResultStream) {
        if (event.TranscriptEvent) {
          const results = event.TranscriptEvent.Transcript.Results;
          
          if (results && results.length > 0) {
            results.forEach((result) => {
              if (result.Alternatives && result.Alternatives.length > 0) {
                const transcript = result.Alternatives[0].Transcript;
                const confidence = result.Alternatives[0].Confidence;
                const isFinal = !result.IsPartial;
                
                if (transcript && transcript.trim()) {
                  hasTranscript = true;
                  const ret = {
                    isFinal: isFinal,
                    transcript: transcript.trim()
                  };
                  
                  console.log(`📝 ${isFinal ? '[FINAL]' : '[PARTIAL]'} "${transcript}" (confidence: ${confidence || 'N/A'})`);
                  this.globalCallback(ret);
                }
              }
            });
          }
        }
      }
      
      if (hasTranscript) {
        console.log("🎉 Transcription completed successfully!");
      } else {
        console.log("⚠️  No speech detected in the audio file.");
        console.log("   This could mean the audio is silent, too quiet, or unclear.");
      }
      
    } catch (err) {
      console.error("❌ Error during transcription:");
      console.error(`   ${err.name}: ${err.message}`);
    }
  }

  getLanguageCode(lang = 'zh') {
    // Map language codes to AWS supported string values
    const langMap = {
      'zh': 'zh-CN',
      'zh-cn': 'zh-CN',
      'en': 'en-US',
      'en-us': 'en-US',
      'ja': 'ja-JP',
      'ko': 'ko-KR',
      'fr': 'fr-FR',
      'de': 'de-DE',
      'es': 'es-ES',
      'it': 'it-IT',
      'pt': 'pt-BR',
      'ru': 'ru-RU',
      'ar': 'ar-SA',
      'hi': 'hi-IN'
    };
    
    return langMap[lang.toLowerCase()] || 'zh-CN';
  }
}

module.exports = {
  AsrClient,
};
