const PORT = 9000
const WebSocket = require('ws')
const express = require('express')
const bodyParser = require('body-parser')
const http = require('http')
const fs = require('fs')
const axios = require('axios')

/* utils */
const createDeepProxy = require('./utils/deepProxy')

/* asr */
// const { AsrClient } = require('./asr_google')
const { AsrClient: AsrClientKeda } = require('./asr_keda')
const { AsrClient: AsrClientSmall } = require('./asr_keda_small')
const { AsrClient: AsrClientDoubao } = require('./asr_doubao')
const { AsrClient: AsrClientMicrosoft } = require('./asr_microsoft')
const { AsrClientSG: AsrClientKedaSG } = require('./asr_keda_sg')
const { AsrClient: AsrClientAli } = require('./asr_ali')
const { AsrClient: AsrClientAws } = require('./asr_aws_simple')
const { recognizeAsrWithWhisper } = require('./whisper_asr')

/* translate */
// const { translateKeda } = require('./translate_keda')
// const { translateDoubao } = require('./translate_doubao')
// const { translateGpt } = require('./translate_gpt')
const { translateDeepseek } = require('./translate_deepseek')
const { resolve } = require('path')

/* tts */
const { synthesizeTextKeda } = require('./tts_keda')
const { synthesizeTextAli } = require('./tts_ali')
// const { synthesizeText } = require('./tts_google')
const { synthesizeSpeech } = require('./tts_microsoft')
const { synthesizeTextKedaSG } = require('./tts_keda_sg')

const app = express()
app.use(bodyParser.json())
app.use(
  bodyParser.urlencoded({
    extended: true,
  })
)
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*') // 允许所有来源
  res.header('Access-Control-Allow-Methods', 'GET,POST,PUT,DELETE,OPTIONS') // 允许的请求方法
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization') // 允许的自定义头
  next()
})

app.get('/tts', async (req, res) => {
  const text = req.query.text
  console.log(new Date().toISOString(), 'received text to tts:', text)
  try {
    const passthrough = await synthesizeText({
      text: text,
      isPassthrough: true,
      format: 'MP3',
    })
    console.log(new Date().toISOString(), 'tts done')
    res.setHeader('Content-Type', 'audio/mp3')
    passthrough.pipe(res)
  } catch (error) {
    console.error('Error processing audio:', error)
    res.status(500).send('Internal Server Error')
  }
})

app.get('/tts_pcm', async (req, res) => {
  const text = req.query.text
  console.log(new Date().toISOString(), 'received text to tts:', text)
  try {
    const passthrough = await synthesizeText({
      text: text,
      isPassthrough: true,
      format: 'LINEAR16',
    })
    console.log(new Date().toISOString(), 'tts done')
    res.setHeader('Content-Type', 'audio/L16')
    passthrough.pipe(res)
  } catch (error) {
    console.error('Error processing audio:', error)
    res.status(500).send('Internal Server Error')
  }
})

http.createServer(app).listen(8000, () => {
  console.log('HTTP Server running on port 8000')
})

const sessions = [] // 用于存储会话信息

const sessionObserverList = new Set() // 观察者列表

// 添加一个sessions的proxy，监听所有sessions的变化
const sessionsProxy = createDeepProxy(sessions, () => {
  console.log('sessions changed:', JSON.stringify(sessions))
  sessionObserverList.forEach((observer) =>
    observer.callback(sessions.slice(), null)
  ) // 复制数组以避免修改原始数据
})

const wss = new WebSocket.Server({
  port: PORT,
  perMessageDeflate: false,
})
const getTime = () => {
  return new Date().toISOString().replace(/T|Z/g, ' ')
}

wss.on('connection', (ws) => {
  console.log('connected')
  // End marker received, process the audio
  let asrLanguage = 'zh'
  let translateLanguage = 'en'
  let asrModel = 0
  let translateModel = 0
  let asrClient = null
  let ttsEnable = false
  let ttsModel = 0

  let isWakeup = false

  let ttsLanguage = 'zh'
  let lastTranslateText = ''

  let currentSessionIndex = 0
  let currentSessionId = ''
  let currentObserverId = new Date().valueOf()
  let currentObserver = {}
  let currentIsBothConnected = false

  let CurrentWhisperRecognizedLanguage = ''
  let StoragedASRStream = Buffer.alloc(0)

  // const tempfilename = `temp_${new Date().valueOf()}.pcm`

  // ws.send(JSON.stringify({ status: 'connected' }))

  ws.on('message', async (data) => {
    // console.log('received:', data.toString())
    // console.log('received:', data)
    try {
      // Check if the message is JSON to detect the end marker
      const message = JSON.parse(data.toString())
      console.log(message)

      if (message.end === true) {
        // end
        asrClient.end()
        console.log(getTime(), 'asr End')
      } else if (message.asrText) {
        asrText = message.asrText
        isPassAsr = true
      } else if (message.personList) {
        personList = message.personList
        deviceList = message.lightList
      } else if (message.asr_language) {
        const sessionId = message.sessionId ? message.sessionId : '-1'
        currentSessionId = sessionId
        console.log(getTime(), 'asr Start', sessionId)
        // 检查sessions中是否有该sessionId
        if (
          sessionId != '-1' &&
          sessionsProxy.find((i) => i.sessionId === sessionId)
        ) {
          console.log('session already exists')

          const sameSessionOtherObserver = [...sessionObserverList].find(
            (i) => i.sessionId === currentSessionId
          )
          if (sameSessionOtherObserver) {
            currentSessionIndex = sameSessionOtherObserver.index === 0 ? 1 : 0
          }

          const resSystemObj = {
            type: 'connect',
            isBothConnected: true,
          }
          currentIsBothConnected = true

          const currentSession = sessionsProxy.find(
            (i) => i.sessionId === sessionId
          )
          currentSession.isBothConnected = true
          currentSession.session1AsrLanguage = message.asr_language
          ws.send(JSON.stringify(resSystemObj))
        } else {
          currentSessionIndex = 0
          sessionsProxy.push({
            sessionId: sessionId,
            messages: [],
            isBothConnected: false,
            session0AsrLanguage: message.asr_language,
          })

          const resSystemObj = {
            type: 'connect',
            isBothConnected: false,
          }
          currentIsBothConnected = false
          ws.send(JSON.stringify(resSystemObj))
        }
        currentObserver = {
          callback: (sessions = [], message) => {
            const session = sessions?.find((i) => i.sessionId === sessionId)
            if (session) {
              // 发送连接状态
              if (session.isBothConnected) {
                const resSystemObj = {
                  type: 'connect',
                  isBothConnected: true,
                }
                if (!currentIsBothConnected) {
                  currentIsBothConnected = true
                  ws.send(JSON.stringify(resSystemObj))
                }
              } else {
                const resSystemObj = {
                  type: 'connect',
                  isBothConnected: false,
                }
                if (currentIsBothConnected) {
                  currentIsBothConnected = false
                  ws.send(JSON.stringify(resSystemObj))
                }
              }
            }

            if (message) {
              ws.send(JSON.stringify(message))
            }
          },
          id: currentObserverId,
          index: currentSessionIndex,
          sessionId: sessionId,
          language: message.asr_language,
        }

        setTimeout(() => {
          sessionObserverList.add(currentObserver)
        }, 75)

        const {
          asr_language,
          translate_language,
          asr_model,
          translate_model,
          tts,
          tts_model,
        } = message

        ttsEnable = tts
        let asrModelText = ''
        let translateModelText = ''
        let ttsModelText = ''

        ttsModel = tts_model
        ttsLanguage = translate_language
        if (asr_model == 0) asrModelText = '科大_大'
        else if (asr_model == 1) asrModelText = '微软'
        else if (asr_model == 2) asrModelText = '谷歌'
        else if (asr_model == -1) asrModelText = '科大_小'
        else if (asr_model == 3) asrModelText = '阿里'
        else if (asr_model == 4) asrModelText = '科大_新加坡'
        else if (asr_model == 5) asrModelText = '豆包'
        else if (asr_model == 6) asrModelText = 'AWS'

        if (translate_model == 0) translateModelText = '科大'
        else if (translate_model == 1) translateModelText = '豆包'
        else if (translate_model == 2) translateModelText = 'deepseek'
        else if (translate_model == 3) translateModelText = 'gpt'

        if (tts) {
          if (tts_model == 0) ttsModelText = '科大'
          else if (tts_model == 1) ttsModelText = '豆包'
          else if (tts_model == 2) ttsModelText = '微软'
          else if (tts_model == 3) ttsModelText = '科大_新加坡'
          else if (tts_model == 4) ttsModelText = '阿里'
        }

        console.log(
          getTime(),
          `asr模型: ${asrModelText}。翻译模型: ${translateModelText}。asr语言:${asr_language}。翻译语言:${translate_language}. TTS模型:${ttsModelText}.tts开启状态:${
            tts ? '开启' : '关闭'
          }`
        )
        const { asrLanguageCode, translateLanguageCode } =
          getTargetLanguageCode(
            asr_model,
            asr_language,
            translate_model,
            translate_language
          )
        asrLanguage = asrLanguageCode
        translateLanguage = translateLanguageCode
        asrModel = asr_model
        translateModel = translate_model

        if (!asrClient) {
          console.log('asrModel:', asrModel)
          if (asrModel == 0) {
            // 科大
            asrClient = new AsrClientKeda(
              '8a6e0fde', // APPID
              'e923b9ef41aef12e2622967b60db5584', // APIKey
              'YTcxNmI2NWYwYzdlNTJiY2JhZDQxMGUx' // APISecret
            )
          } else if (asrModel == 1) {
            // 微软
            asrClient = new AsrClientMicrosoft(asrLanguage)
            asrClient.init()
          } else if (asrModel == 2) {
            asrClient = new AsrClient('LINEAR16', 16000, asrLanguage)
            // gpt whisper
          } else if (asrModel == -1) {
            asrClient = new AsrClientSmall(asrLanguage)
            asrClient.start()
          } else if (asrModel == 3) {
            asrClient = new AsrClientAli(asrLanguage)
          } else if (asrModel == 4) {
            asrClient = new AsrClientKedaSG(asrLanguage)
          } else if (asrModel == 5) {
            asrClient = new AsrClientDoubao(asrLanguage)
            asrClient.init()
          } else if (asrModel == 6) {
            asrClient = new AsrClientAws(asrLanguage)
          }
        }
      } else {
        try {
        } catch (error) {
          console.log(error)
          ws.send(
            JSON.stringify({
              status: 'error',
              message: error.message,
            })
          )
        }
      }
    } catch (error) {
      // console.log(error)
      ;(async () => {
        // 如果还未识别到whisper语言就先存储到本地
        if (!CurrentWhisperRecognizedLanguage) {
          StoragedASRStream = Buffer.concat([StoragedASRStream, data])
        }

        asrClient?.requestAsr(
          data,
          async (res) => {
            const retObj = {
              ...res,
              type: 'asr',
            }

            const startTranslate = () => {
              console.log('start translate')
              if (asrLanguage != CurrentWhisperRecognizedLanguage) return

              let targetText = ''
              let isFirstTranslate = true
              // 双向监听服务器相关
              const currentSession = sessionsProxy.find(
                (i) =>
                  i.index !== currentSessionIndex &&
                  i.sessionId === currentSessionId
              )
              let lang = translateLanguage
              if (currentSession && currentSession.isBothConnected) {
                lang =
                  currentSessionIndex === 0
                    ? currentSession.session1AsrLanguage
                    : currentSession.session0AsrLanguage

                const sameSessionOtherObserver = [...sessionObserverList].find(
                  (i) =>
                    i.index !== currentSessionIndex &&
                    i.sessionId === currentSessionId
                )
                console.log(
                  'sameSessionOtherObserver',
                  JSON.stringify(sameSessionOtherObserver)
                )
                if (sameSessionOtherObserver) {
                  lang = sameSessionOtherObserver.language
                }
              }

              const paramArr = [
                res.transcript,
                lang,
                async (resTranslate) => {
                  targetText += resTranslate.target_text
                  // 如果targetText起始是&&，则认为是唤醒词
                  if (targetText.startsWith('&&') && !isWakeup) {
                    targetText = targetText.slice(2)
                    isWakeup = true
                    const lastWakeupTime = new Date().valueOf()

                    const resSystemObj = {
                      type: 'system',
                      content: '',
                      imageUrls: [],
                      lastSystemTime: lastWakeupTime,
                      isFinished: false,
                      isStream: true,
                      isStart: true,
                    }
                    ws.send(JSON.stringify(resSystemObj))

                    const sameSessionOtherObserver = [
                      ...sessionObserverList,
                    ].find(
                      (i) =>
                        i.index !== currentSessionIndex &&
                        i.sessionId === currentSessionId
                    )
                    if (sameSessionOtherObserver) {
                      sameSessionOtherObserver.callback(null, resSystemObj)
                    }

                    // 下面的接口采用sse返回
                    const url = 'http://localhost:3001/api/search/stream'
                    const config = {
                      method: 'post',
                      url: url,
                      data: {
                        query: resTranslate.source_text,
                      },
                      responseType: 'stream',
                    }

                    const response = await axios(config)
                    let content = ''
                    let responseData = ''
                    let partialJSON = ''

                    response.data.on('data', (chunk) => {
                      const data = chunk.toString().trim().slice(6)
                      partialJSON += data

                      try {
                        // 尝试解析完整的JSON
                        const parsedData = JSON.parse(partialJSON)
                        if (parsedData.response) {
                          content += parsedData.response
                          partialJSON = ''

                          try {
                            // 使用正则表达式提取"response"字段的内容
                            const responseMatch =
                              /"response"\s*:\s*"([^"]*)/i.exec(content)
                            if (responseMatch && responseMatch[1]) {
                              // 提取到部分response内容
                              responseData = responseMatch[1]

                              const resSystemObj = {
                                type: 'system',
                                content: responseData,
                                imageUrls: [],
                                lastSystemTime: lastWakeupTime,
                                isFinished: false,
                                isStream: true,
                                isStart: false,
                              }
                              ws.send(JSON.stringify(resSystemObj))
                            }
                          } catch (regexError) {
                            // 正则提取失败，忽略
                          }
                        } else if (parsedData.success) {
                          const resSystemObj = {
                            type: 'system',
                            content: responseData,
                            imageUrls: parsedData.data.imageUrls.filter(
                              (_, index) => index < 3
                            ),
                            lastSystemTime: lastWakeupTime,
                            isFinished: true,
                            isStream: false,
                            isStart: false,
                          }
                          ws.send(JSON.stringify(resSystemObj))
                        }
                      } catch (error) {}
                    })

                    response.data.on('error', (error) => {
                      console.error('SSE连接错误:', error)
                    })

                    if (currentSession?.isBothConnected) {
                      let targetLanguage =
                        currentSessionIndex === 0
                          ? currentSession.session1AsrLanguage
                          : currentSession.session0AsrLanguage

                      const sameSessionOtherObserver = [
                        ...sessionObserverList,
                      ].find(
                        (i) =>
                          i.index !== currentSessionIndex &&
                          i.sessionId === currentSessionId
                      )
                      if (sameSessionOtherObserver) {
                        targetLanguage = sameSessionOtherObserver.language
                      }

                      const url = 'http://localhost:3001/api/search/stream'
                      const config = {
                        method: 'post',
                        url: url,
                        data: {
                          query: resTranslate.source_text,
                          target_language: targetLanguage,
                        },
                        responseType: 'stream',
                      }

                      const response = await axios(config)
                      let content = ''
                      let responseData = ''
                      let partialJSON = ''

                      response.data.on('data', (chunk) => {
                        const data = chunk.toString().trim().slice(6)
                        partialJSON += data

                        try {
                          // 尝试解析完整的JSON
                          const parsedData = JSON.parse(partialJSON)
                          if (parsedData.response) {
                            content += parsedData.response
                            partialJSON = ''

                            try {
                              // 使用正则表达式提取"response"字段的内容
                              const responseMatch =
                                /"response"\s*:\s*"([^"]*)/i.exec(content)
                              if (responseMatch && responseMatch[1]) {
                                // 提取到部分response内容
                                responseData = responseMatch[1]

                                const resSystemObj = {
                                  type: 'system',
                                  content: responseData,
                                  imageUrls: [],
                                  lastSystemTime: lastWakeupTime,
                                  isFinished: false,
                                  isStream: true,
                                  isStart: false,
                                }

                                const sameSessionOtherObserver = [
                                  ...sessionObserverList,
                                ].find(
                                  (i) =>
                                    i.index !== currentSessionIndex &&
                                    i.sessionId === currentSessionId
                                )
                                if (sameSessionOtherObserver) {
                                  sameSessionOtherObserver.callback(
                                    null,
                                    resSystemObj
                                  )
                                }
                              }
                            } catch (regexError) {
                              // 正则提取失败，忽略
                            }
                          } else if (parsedData.success) {
                            const resSystemObj = {
                              type: 'system',
                              content: responseData,
                              imageUrls: parsedData.data.imageUrls.filter(
                                (_, index) => index < 3
                              ),
                              lastSystemTime: lastWakeupTime,
                              isFinished: true,
                              isStream: false,
                              isStart: false,
                            }

                            const sameSessionOtherObserver = [
                              ...sessionObserverList,
                            ].find(
                              (i) =>
                                i.index !== currentSessionIndex &&
                                i.sessionId === currentSessionId
                            )
                            if (sameSessionOtherObserver) {
                              sameSessionOtherObserver.callback(
                                null,
                                resSystemObj
                              )
                            }
                          }
                        } catch (error) {}
                      })

                      response.data.on('error', (error) => {
                        console.error('SSE连接错误:', error)
                      })
                    }
                  }

                  const retTranslateObj = {
                    type: 'translate',
                    target_text: targetText,
                    finish_reason: isFirstTranslate
                      ? 'start'
                      : resTranslate.finish_reason,
                    source_text: resTranslate.source_text,
                    target_audio: '',
                    translate_id: resTranslate.translate_id, // 用于判断是否是同一个翻译任务
                    session_index: currentSessionIndex,
                  }
                  isFirstTranslate = false

                  if (resTranslate.finish_reason === 'stop') {
                    isWakeup = false
                    lastTranslateText = targetText
                  }

                  if (retTranslateObj.finish_reason === 'start') {
                    if (targetText === lastTranslateText) {
                      // bug 跳过
                      return
                    }
                  }

                  if (resTranslate.finish_reason === 'stop' && ttsEnable) {
                    const target_audio = await generateTts({
                      text: targetText,
                      languageCode: ttsLanguage,
                      ttsModel,
                    })
                    retTranslateObj.target_audio = target_audio
                      ? target_audio
                      : ''
                  }

                  console.log(
                    getTime(),
                    'translate:',
                    retTranslateObj.target_text
                  )
                  if (currentSessionId === '-1') {
                    ws.send(JSON.stringify(retTranslateObj))
                  } else {
                    const sameSessionOtherObserver = [
                      ...sessionObserverList,
                    ].find(
                      (i) =>
                        i.index !== currentSessionIndex &&
                        i.sessionId === currentSessionId
                    )
                    if (sameSessionOtherObserver) {
                      sameSessionOtherObserver.callback(null, retTranslateObj)
                    }
                  }
                },
              ]

              if (res.transcript) {
                if (translateModel == 0) {
                  // 科大
                  translateKeda(...paramArr)
                } else if (translateModel == 1) {
                  // 豆包
                  translateDoubao(...paramArr)
                } else if (translateModel == 2) {
                  // deepseek
                  translateDeepseek(...paramArr)
                } else if (translateModel == 3) {
                  // gpt
                  translateGpt(...paramArr)
                }
              }
            }

            // 若本次已识别了语言且还未得到WhisperLanguage，就发送给Whisper进行识别一次
            if (!CurrentWhisperRecognizedLanguage) {
              console.log('start whisper recognize')
              CurrentWhisperRecognizedLanguage = 'null'
              retObj.isFinal = false
              recognizeAsrWithWhisper(StoragedASRStream).then((resWhisper) => {
                const { text, language } = resWhisper
                CurrentWhisperRecognizedLanguage = language
                console.log(
                  'whisper recognized language:',
                  CurrentWhisperRecognizedLanguage,
                  'Current asr language:',
                  asrLanguage
                )
                console.log('whisper text', text)

                if (asrLanguage != CurrentWhisperRecognizedLanguage) {
                  console.log('检测到语言不同')

                  let resMessage =
                    '检测到当前语音可能为中文，是否要切换至中文？'
                  if (CurrentWhisperRecognizedLanguage == 'en')
                    resMessage =
                      'The current voice is detected to be English. Do you want to switch to Englist?'
                  else if (CurrentWhisperRecognizedLanguage == 'ja')
                    resMessage =
                      '現在の言語は日本語です。日本語に切り替えますか？'
                  else if (CurrentWhisperRecognizedLanguage == 'ko')
                    resMessage =
                      '현재 음성이 한국어로 감지되었습니다. 한국어로 전환하시겠습니까?'
                  else if (CurrentWhisperRecognizedLanguage == 'ru')
                    resMessage =
                      'Текущий голос определен как русский. Хотите переключиться на русский?'
                  else if (CurrentWhisperRecognizedLanguage == 'fr')
                    resMessage =
                      'La voix actuelle est détectée comme étant française. Voulez-vous basculer en français ?'
                  else if (CurrentWhisperRecognizedLanguage == 'es')
                    resMessage =
                      'La voz actual se ha detectado como español. ¿Desea cambiar a español?'
                  else if (CurrentWhisperRecognizedLanguage == 'id')
                    resMessage =
                      'Suara saat ini terdeteksi sebagai bahasa Indonesia. Apakah Anda ingin beralih ke bahasa Indonesia?'
                  else if (CurrentWhisperRecognizedLanguage == 'th')
                    resMessage =
                      'เสียงปัจจุบันถูกตรวจจับว่าเป็นภาษาไทย คุณต้องการเปลี่ยนเป็นภาษาไทยหรือไม่?'

                  const resMessageObj = {
                    type: 'message',
                    content: resMessage,
                  }
                  ws.send(JSON.stringify(resMessageObj))
                }
              })
            }

            if (res.isFinal) {
              startTranslate()
            } else {
            }
            if (res.transcript) {
              ws.send(JSON.stringify(retObj))
            }
          },
          asrLanguage
        )
      })()

      // fs.appendFileSync(tempfilename, data)
    }
  })

  ws.on('close', () => {
    console.log('Client disconnected')
    asrClient?.end()

    sessionObserverList.delete(currentObserver)

    const currentSession = sessionsProxy.find(
      (i) => i.sessionId === currentSessionId
    )
    const isBothConnected = currentSession.isBothConnected
    if (!isBothConnected) {
      sessionsProxy.splice(
        sessionsProxy.findIndex((i) => i.sessionId === currentSessionId),
        1
      )
    } else {
      currentSession.isBothConnected = false
    }
  })

  ws.on('error', (error) => {
    console.error('WebSocket error:', error)
  })
})

function endsWithChinesePunctuation(str) {
  // 定义一个包含中文结束标点符号的数组
  const punctuationMarks = ['。', '？', '！']

  // 获取字符串的最后一个字符
  const lastChar = str.slice(-1)

  // 判断最后一个字符是否在标点符号数组中
  return punctuationMarks.includes(lastChar)
}

function getTargetLanguageCode(
  asrModel,
  asrLang,
  translateModel,
  translateLang
) {
  let asrLanguageCode = ''
  let translateLanguageCode = ''
  if (asrModel == 0) {
    // 科大

    if (asrLang === 'zh') asrLanguageCode = 'zh'
    else if (asrLang === 'en') asrLanguageCode = 'en'
    else if (asrLang === 'ja') asrLanguageCode = 'ja'
    else if (asrLang === 'ko') asrLanguageCode = 'ko'
    else if (asrLang === 'ru') asrLanguageCode = 'ru'
    else if (asrLang === 'fr') asrLanguageCode = 'fr'
    else if (asrLang === 'es') asrLanguageCode = 'es'
    else if (asrLang === 'id') asrLanguageCode = 'id'
    else if (asrLang === 'th') asrLanguageCode = 'th'
    else if (asrLang === 'pt') asrLanguageCode = 'pt'
    else if (asrLang === 'vi') asrLanguageCode = 'vi'
    else if (asrLang === 'ar') asrLanguageCode = 'ar'
  } else if (asrModel == 1) {
    // 微软

    if (asrLang === 'zh') asrLanguageCode = 'zh-CN'
    else if (asrLang === 'en') asrLanguageCode = 'en-US'
    else if (asrLang === 'ja') asrLanguageCode = 'ja-JP'
    else if (asrLang === 'ko') asrLanguageCode = 'ko-KR'
    else if (asrLang === 'ru') asrLanguageCode = 'ru-RU'
    else if (asrLang === 'fr') asrLanguageCode = 'fr-FR'
    else if (asrLang === 'es') asrLanguageCode = 'es-ES'
    else if (asrLang === 'id') asrLanguageCode = 'id-ID'
    else if (asrLang === 'th') asrLanguageCode = 'th-TH'
    else if (asrLang === 'pt') asrLanguageCode = 'pt-PT'
    else if (asrLang === 'vi') asrLanguageCode = 'vi-VN'
    else if (asrLang === 'ar') asrLanguageCode = 'ar-AE'
  } else if (asrModel == 2) {
    // 谷歌

    if (asrLang === 'zh') asrLanguageCode = 'cmn-Hans-CN'
    else if (asrLang === 'en') asrLanguageCode = 'en-US'
    else if (asrLang === 'ja') asrLanguageCode = 'ja-JP'
    else if (asrLang === 'ko') asrLanguageCode = 'ko-KR'
    else if (asrLang === 'ru') asrLanguageCode = 'ru-RU'
    else if (asrLang === 'fr') asrLanguageCode = 'fr-FR'
    else if (asrLang === 'es') asrLanguageCode = 'es-ES'
    else if (asrLang === 'id') asrLanguageCode = 'id-ID'
    else if (asrLang === 'th') asrLanguageCode = 'th-TH'
    else if (asrLang === 'pt') asrLanguageCode = 'pt-PT'
    else if (asrLang === 'vi') asrLanguageCode = 'vi-VN'
    else if (asrLang === 'ar') asrLanguageCode = 'ar-AE'
  } else if (asrModel == -1) {
    if (asrLang === 'zh') asrLanguageCode = 'zh_cn'
    else if (asrLang === 'en') asrLanguageCode = 'en_us'
    else if (asrLang === 'ja') asrLanguageCode = 'ja_jp'
    else if (asrLang === 'ko') asrLanguageCode = 'ko_kr'
    else if (asrLang === 'ru') asrLanguageCode = 'ru_ru'
    else if (asrLang === 'fr') asrLanguageCode = 'fr_fr'
    else if (asrLang === 'es') asrLanguageCode = 'es_es'
    else if (asrLang === 'id') asrLanguageCode = 'id_ID'
    else if (asrLang === 'th') asrLanguageCode = 'th_TH'
    else if (asrLang === 'pt') asrLanguageCode = 'pt_PT'
    else if (asrLang === 'vi') asrLanguageCode = 'vi_VN'
    else if (asrLang === 'ar') asrLanguageCode = 'ar_il'
  } else if (asrModel == 4) {
    if (asrLang === 'zh') asrLanguageCode = 'zh_cn'
    else if (asrLang === 'en') asrLanguageCode = 'en_us'
    else if (asrLang === 'ja') asrLanguageCode = 'ja_jp'
    else if (asrLang === 'ko') asrLanguageCode = 'ko_kr'
    else if (asrLang === 'ru') asrLanguageCode = 'ru-ru'
    else if (asrLang === 'fr') asrLanguageCode = 'fr_fr'
    else if (asrLang === 'es') asrLanguageCode = 'es_es'
    else if (asrLang === 'id') asrLanguageCode = 'id_ID'
    else if (asrLang === 'th') asrLanguageCode = 'th_TH'
    else if (asrLang === 'pt') asrLanguageCode = 'en_us' // 无葡萄牙语
    else if (asrLang === 'vi') asrLanguageCode = 'vi_VN'
    else if (asrLang === 'ar') asrLanguageCode = 'ar_il'
  } else if (asrModel == 5) {
    if (asrLang === 'zh') asrLanguageCode = 'volcengine_streaming_common'
    else if (asrLang === 'en') asrLanguageCode = 'volcengine_streaming_en'
    else if (asrLang === 'ja') asrLanguageCode = 'volcengine_streaming_ja'
    else if (asrLang === 'ko') asrLanguageCode = 'volcengine_streaming_ko'
    else if (asrLang === 'ru') asrLanguageCode = 'volcengine_streaming_ru'
    else if (asrLang === 'fr') asrLanguageCode = 'volcengine_streaming_fr_fr'
    else if (asrLang === 'es') asrLanguageCode = 'volcengine_streaming_es_mx'
    else if (asrLang === 'id') asrLanguageCode = 'volcengine_streaming_id'
    else if (asrLang === 'th')
      asrLanguageCode = 'volcengine_streaming_en' // 无泰语
    else if (asrLang === 'pt')
      asrLanguageCode = 'volcengine_streaming_en' // 无葡萄牙语
    else if (asrLang === 'vi')
      asrLanguageCode = 'volcengine_streaming_en' // 无越南语
    else if (asrLang === 'ar') asrLanguageCode = 'volcengine_streaming_en' // 无阿拉伯语
  } else if (asrModel == 6) {
    // AWS 语音识别模型
    if (asrLang === 'zh') asrLanguageCode = 'zh-CN'
    else if (asrLang === 'en') asrLanguageCode = 'en-US'
    else if (asrLang === 'ja') asrLanguageCode = 'ja-JP'
    else if (asrLang === 'ko') asrLanguageCode = 'ko-KR'
    else if (asrLang === 'ru') asrLanguageCode = 'ru-RU'
    else if (asrLang === 'fr') asrLanguageCode = 'fr-FR'
  }

  translateLanguageCode = translateLang

  return {
    asrLanguageCode,
    translateLanguageCode,
  }
}

async function sleep(time) {
  console.log('sleep')
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log('sleep end')
      resolve(true)
    }, time)
  })
}

async function generateTts({ text = '', languageCode = '', ttsModel = 0 }) {
  let targetAudio = null

  return new Promise(async (resolve) => {
    if (ttsModel == 0) {
      // 科大
      targetAudio = await synthesizeTextKeda({
        text,
        languageCode,
      })
    } else if (ttsModel === 1) {
      // 豆包
    } else if (ttsModel === 2) {
      // 微软
      targetAudio = await synthesizeSpeech({
        text,
        languageCode,
      })
    } else if (ttsModel === 3) {
      // 科大_新加坡
      targetAudio = await synthesizeTextKedaSG({
        text,
        languageCode,
      })
    } else if (ttsModel === 4) {
      // 阿里
      targetAudio = await synthesizeTextAli({
        text,
        languageCode,
      })
    }

    resolve(targetAudio)
  })
}
