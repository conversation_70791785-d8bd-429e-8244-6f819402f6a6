const {
  TranscribeStreamingClient,
  StartStreamTranscriptionCommand,
} = require("@aws-sdk/client-transcribe-streaming");

const getTime = () => {
  return new Date().toISOString().replace(/T|Z/g, ' ')
}

// Function to find the start of PCM data in a WAV file
function findPCMDataStart(buffer) {
  // Look for "data" chunk marker
  for (let i = 0; i < buffer.length - 4; i++) {
    if (buffer[i] === 0x64 && buffer[i + 1] === 0x61 && 
        buffer[i + 2] === 0x74 && buffer[i + 3] === 0x61) {
      // Found "data" - PCM data starts 8 bytes later (4 for "data" + 4 for size)
      return i + 8;
    }
  }
  return 44; // Default WAV header size if we can't find data chunk
}

class AsrClient {
  constructor(lang = 'zh') {
    this.lang = lang;
    this.globalCallback = () => {};
    this.isStarted = false;
    this.isStarting = false;
    this.isTranscribing = false;
    this.audioBuffer = [];
    this.vadTimeOut = null;
    this.lastAsrText = '';

    // Use exact same credentials and setup as your working demo
    this.credentials = {
      accessKeyId: "********************",
      secretAccessKey: "hkSxaZ4RCWxVpgIbn+Xmo5LiaAxlfv7UVbSSYQyW",
      sessionToken: "IQoJb3JpZ2luX2VjEKz//////////wEaCXVzLXdlc3QtMSJGMEQCIAlxSGKOrpUip/bZMHThWgFBHOGb0jByBv5vw28yFR0hAiAC5oI0rUhNKHfR6pBhrPXqZMpZrVN3skFQicci7cHY6Sr5Agi1//////////8BEAMaDDE3NTM3NjkzNzg2NyIMEz3183hI4qeSB/CYKs0CE1YKqF1Jrgqkr/lHrjNoiy6dZIBapSJYoYhFQhKSr4OP872dso9+qDIRI1/++ZoucpWyEx6ZjM9esHGq7IqcwKJi3Cup5rZXPQSPFJxT3IEzAdhIgilO4pwZTk174zDdkuucRGew0Bmwj+KTLZSE04rmLGD4I57nuuz99bS4oPauFMc7lP10g6X2mpiq7MfDX7Oyz1AWJOBSiXWH245pW+Ju8uyNkqXpTeGKZfFpMe8h8re9Hd6prGH8cwRqvaqx3Sx5iHClJIgN99+kwtAPnZi/t5Pf9vYnOeSC8ivzcmnkZGsGSiY3g6tF4QU+KD/2grF/LGMknKGAggXeafTnZeNRPycEE6/BYzIKMKZTY1Lix3zx5Sa9x2GozDXTYOY2X/xD61dyekCkscZyYK7nkHlSZkyoKiV9FLQ08r32fq5hN2dblVKCWZIgkZ7bMNnnvMMGOqgBk+tLmv6XDuFDshiR+b9gAKFzITNFCBQgHjI95V4MdPPKmaMmN91lpPpEyaFKDfw1PHrvSFMLSHVitjCQ4oxn1/7ySYNanFCCww3ZCM09z9Scr2wUeuzCSqb/09x8wxL4GOuW+tBjNxnXfT74y8P5SBAJ18g0ObiqQzp40CpAh/S/L5fV62Hirtpj6W4zspK2TA4bEXy702jSMQ593Rh3uHr4TW/9I0Je"
    };
  }

  async start() {
    if (this.isStarted || this.isStarting) {
      return true;
    }

    this.isStarting = true;
    console.log(getTime(), 'aws asr started');
    this.isStarted = true;
    this.isStarting = false;
    return true;
  }

  async end() {
    this.isStarted = false;
    this.isTranscribing = false;
    this.audioBuffer = [];
    if (this.vadTimeOut) {
      clearTimeout(this.vadTimeOut);
      this.vadTimeOut = null;
    }
  }

  async requestAsr(audioData, callback = () => {}, lang = null) {
    // Prevent multiple simultaneous transcriptions
    if (this.isTranscribing) {
      console.log('AWS ASR: Already transcribing, skipping...');
      return;
    }

    // Ensure we're started
    if (!this.isStarted && !this.isStarting) {
      await this.start();
    }

    if (this.isStarting) {
      // Wait for start to complete
      await new Promise(resolve => {
        const checkInterval = setInterval(() => {
          if (!this.isStarting) {
            clearInterval(checkInterval);
            resolve();
          }
        }, 10);
      });
    }

    this.globalCallback = callback;
    const useLanguage = lang || this.lang;

    // Add to buffer instead of processing immediately
    this.addToBuffer(audioData);

    // Process buffer with debouncing (like other ASR clients)
    this.scheduleTranscription(useLanguage);
  }

  addToBuffer(audioData) {
    // Skip very small chunks (likely noise)
    if (audioData.length < 1000) {
      console.log('AWS ASR: Skipping small audio chunk:', audioData.length, 'bytes');
      return;
    }

    this.audioBuffer.push(audioData);
    console.log('AWS ASR: Added to buffer, total chunks:', this.audioBuffer.length);
  }

  scheduleTranscription(lang) {
    // Clear existing timeout
    if (this.vadTimeOut) {
      clearTimeout(this.vadTimeOut);
    }

    // Schedule transcription after a delay (like Ali ASR)
    this.vadTimeOut = setTimeout(async () => {
      if (this.audioBuffer.length > 0 && !this.isTranscribing) {
        await this.processBuffer(lang);
      }
    }, 800); // 800ms delay like other ASR clients
  }

  async processBuffer(lang) {
    if (this.isTranscribing || this.audioBuffer.length === 0) {
      return;
    }

    this.isTranscribing = true;

    try {
      // Combine all buffered audio
      const totalLength = this.audioBuffer.reduce((sum, chunk) => sum + chunk.length, 0);
      const combinedAudio = Buffer.alloc(totalLength);
      let offset = 0;

      for (const chunk of this.audioBuffer) {
        chunk.copy(combinedAudio, offset);
        offset += chunk.length;
      }

      console.log(`AWS ASR: Processing ${this.audioBuffer.length} chunks, ${totalLength} bytes total`);

      // Clear buffer
      this.audioBuffer = [];

      // Use the EXACT same code structure as your working demo
      await this.startRequest(combinedAudio, lang);

    } finally {
      this.isTranscribing = false;
    }
  }

  async startRequest(audioData, lang) {
    console.log("🚀 Starting AWS Transcribe Streaming...");

    try {
      const client = new TranscribeStreamingClient({
        region: "us-east-1",
        credentials: this.credentials,
        maxAttempts: 1,
      });

      // Skip WAV header and get PCM data
      let pcmDataStart = findPCMDataStart(audioData);
      let dataToProcess = audioData.slice(pcmDataStart);

      console.log(`🔍 WAV header detected, PCM data starts at byte ${pcmDataStart}`);
      console.log(`📊 PCM data size: ${dataToProcess.length} bytes`);

      // Check if we have enough audio data (at least 1 second at 16kHz, 16-bit = 32000 bytes)
      if (dataToProcess.length < 16000) {
        console.log("⚠️  Audio data too short for reliable transcription");
        // Still try to process but with lower expectations
      }

      // Create audio stream that sends appropriate chunks
      const audioStream = async function* () {
        let chunkCount = 0;
        let totalBytes = 0;
        const maxChunkSize = 1024 * 8; // 8KB chunks for better performance

        // Break data into chunks
        for (let i = 0; i < dataToProcess.length; i += maxChunkSize) {
          const smallChunk = dataToProcess.slice(i, i + maxChunkSize);
          chunkCount++;
          totalBytes += smallChunk.length;

          console.log(`📦 Chunk ${chunkCount}: ${smallChunk.length} bytes PCM data`);
          yield { AudioEvent: { AudioChunk: smallChunk } };

          // Smaller delay for better real-time performance
          await new Promise(resolve => setTimeout(resolve, 20));
        }

        console.log(`✅ Audio stream complete: ${chunkCount} chunks, ${totalBytes} bytes PCM data`);
      };

      // Use exact same constants as your demo
      const LanguageCode = this.getLanguageCode(lang);
      const MediaEncoding = "pcm";
      const MediaSampleRateHertz = 16000;

      const params = {
        LanguageCode,
        MediaEncoding,
        MediaSampleRateHertz,
        AudioStream: audioStream(),
      };

      console.log("📡 Sending transcription request...");
      const command = new StartStreamTranscriptionCommand(params);
      const response = await client.send(command);
      
      console.log("🎧 Listening for transcription results...");
      let hasTranscript = false;
      
      for await (const event of response.TranscriptResultStream) {
        if (event.TranscriptEvent) {
          const results = event.TranscriptEvent.Transcript.Results;

          if (results && results.length > 0) {
            results.forEach((result) => {
              if (result.Alternatives && result.Alternatives.length > 0) {
                const transcript = result.Alternatives[0].Transcript;
                const confidence = result.Alternatives[0].Confidence;
                const isFinal = !result.IsPartial;

                if (transcript && transcript.trim()) {
                  hasTranscript = true;
                  this.lastAsrText = transcript.trim();

                  const ret = {
                    isFinal: false, // First send as partial
                    transcript: this.lastAsrText
                  };

                  console.log(getTime(), 'transcript:', this.lastAsrText);
                  this.globalCallback(ret);

                  // If it's final or we have good confidence, also send final result
                  if (isFinal || (confidence && confidence > 0.8)) {
                    setTimeout(() => {
                      const finalRet = {
                        isFinal: true,
                        transcript: this.lastAsrText
                      };
                      this.globalCallback(finalRet);
                    }, 100);
                  }
                }
              }
            });
          }
        }
      }
      
      if (hasTranscript) {
        console.log("🎉 Transcription completed successfully!");
      } else {
        console.log("⚠️  No speech detected in the audio file.");
        console.log("   This could mean the audio is silent, too quiet, or unclear.");
      }
      
    } catch (err) {
      console.error("❌ Error during transcription:");
      console.error(`   ${err.name}: ${err.message}`);
    }
  }

  getLanguageCode(lang = 'zh') {
    const langMap = {
      'zh': 'zh-CN',
      'zh-cn': 'zh-CN',
      'en': 'en-US',
      'en-us': 'en-US',
      'ja': 'ja-JP',
      'ko': 'ko-KR',
      'fr': 'fr-FR',
      'de': 'de-DE',
      'es': 'es-ES',
      'it': 'it-IT',
      'pt': 'pt-BR',
      'ru': 'ru-RU',
      'ar': 'ar-SA',
      'hi': 'hi-IN'
    };
    
    return langMap[lang.toLowerCase()] || 'zh-CN';
  }
}

module.exports = {
  AsrClient,
};
