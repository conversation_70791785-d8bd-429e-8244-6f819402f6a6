const {
  TranscribeStreamingClient,
  StartStreamTranscriptionCommand,
  LanguageCode,
  MediaEncoding,
} = require("@aws-sdk/client-transcribe-streaming");

const getTime = () => {
  return new Date().toISOString().replace(/T|Z/g, ' ')
}

// Function to find the start of PCM data in a WAV file
function findPCMDataStart(buffer) {
  // Look for "data" chunk marker
  for (let i = 0; i < buffer.length - 4; i++) {
    if (buffer[i] === 0x64 && buffer[i + 1] === 0x61 && 
        buffer[i + 2] === 0x74 && buffer[i + 3] === 0x61) {
      // Found "data" - PCM data starts 8 bytes later (4 for "data" + 4 for size)
      return i + 8;
    }
  }
  return 44; // Default WAV header size if we can't find data chunk
}

class AsrClient {
  constructor(credentials = null, region = "us-east-1") {
    this.credentials = credentials;
    this.region = region;
    this.client = null;
    this.isStarted = false;
    this.isStarting = false;
    this.globalCallback = () => {};
    this.audioBuffer = [];
    this.headerSkipped = false;
    this.maxChunkSize = 1024 * 4; // 4KB max chunks
    this.currentStream = null;
    this.isTranscribing = false;
  }

  async start() {
    if (this.isStarted || this.isStarting) {
      return true;
    }

    this.isStarting = true;
    
    try {
      this.client = new TranscribeStreamingClient({
        region: this.region,
        credentials: this.credentials,
        maxAttempts: 1,
      });
      
      this.isStarted = true;
      this.isStarting = false;
      console.log(getTime(), 'aws asr started');
      return true;
    } catch (error) {
      this.isStarting = false;
      console.error('AWS ASR start error:', error);
      return false;
    }
  }

  async end() {
    this.isStarted = false;
    this.client = null;
    this.audioBuffer = [];
    this.headerSkipped = false;
    this.isTranscribing = false;
    if (this.currentStream) {
      this.currentStream = null;
    }
  }

  async requestAsr(audioData, callback = () => {}, lang = 'zh') {
    if (!this.isStarted && !this.isStarting) {
      await this.start();
    }

    if (this.isStarting) {
      // Wait for start to complete
      await new Promise(resolve => {
        const checkInterval = setInterval(() => {
          if (!this.isStarting) {
            clearInterval(checkInterval);
            resolve();
          }
        }, 10);
      });
    }

    this.globalCallback = callback;
    await this.processAudioData(audioData, lang);
  }

  async processAudioData(audioData, lang = 'zh') {
    let dataToProcess = audioData;

    // Skip WAV header if this is the first chunk
    if (!this.headerSkipped && audioData.length > 44) {
      const pcmDataStart = findPCMDataStart(audioData);
      if (pcmDataStart > 0) {
        console.log(`🔍 WAV header detected, PCM data starts at byte ${pcmDataStart}`);
        dataToProcess = audioData.slice(pcmDataStart);
      }
      this.headerSkipped = true;
    }

    // Start transcription immediately with this chunk
    await this.transcribeChunk(dataToProcess, lang);
  }

  async transcribeChunk(audioData, lang = 'zh') {
    if (!this.client || this.isTranscribing) {
      return;
    }

    this.isTranscribing = true;

    try {
      // Create audio stream from single chunk
      const audioStream = async function* (data, maxChunkSize) {
        // Break large chunks into smaller pieces
        for (let i = 0; i < data.length; i += maxChunkSize) {
          const smallChunk = data.slice(i, i + maxChunkSize);
          yield { AudioEvent: { AudioChunk: smallChunk } };

          // Small delay between chunks
          await new Promise(resolve => setTimeout(resolve, 10));
        }
      };

      const languageCode = this.getLanguageCode(lang);

      // Ensure proper parameter structure for AWS SDK
      const params = {
        LanguageCode: languageCode, // Use enum value directly
        MediaEncoding: MediaEncoding.PCM, // Use enum value
        MediaSampleRateHertz: 16000,
        AudioStream: audioStream(audioData, this.maxChunkSize),
      };

      console.log('AWS Transcribe params:', {
        LanguageCode: params.LanguageCode,
        MediaEncoding: params.MediaEncoding,
        MediaSampleRateHertz: params.MediaSampleRateHertz
      });

      const command = new StartStreamTranscriptionCommand(params);
      const response = await this.client.send(command);

      for await (const event of response.TranscriptResultStream) {
        if (event.TranscriptEvent) {
          const results = event.TranscriptEvent.Transcript.Results;

          if (results && results.length > 0) {
            results.forEach((result) => {
              if (result.Alternatives && result.Alternatives.length > 0) {
                const transcript = result.Alternatives[0].Transcript;
                const isFinal = !result.IsPartial;

                if (transcript && transcript.trim()) {
                  const ret = {
                    isFinal: isFinal,
                    transcript: transcript.trim()
                  };

                  console.log(getTime(), 'transcript:', transcript.trim());
                  this.globalCallback(ret);
                }
              }
            });
          }
        }
      }

    } catch (err) {
      console.error("AWS transcription error:", err.message);
      console.error("Full error:", err);
    } finally {
      this.isTranscribing = false;
    }
  }

  getLanguageCode(lang = 'zh') {
    // Map language codes to AWS LanguageCode enum values
    const langMap = {
      'zh': LanguageCode.ZH_CN,
      'zh-cn': LanguageCode.ZH_CN,
      'en': LanguageCode.EN_US,
      'en-us': LanguageCode.EN_US,
      'ja': LanguageCode.JA_JP,
      'ko': LanguageCode.KO_KR,
      'fr': LanguageCode.FR_FR,
      'de': LanguageCode.DE_DE,
      'es': LanguageCode.ES_ES,
      'it': LanguageCode.IT_IT,
      'pt': LanguageCode.PT_BR,
      'ru': LanguageCode.RU_RU,
      'ar': LanguageCode.AR_SA,
      'hi': LanguageCode.HI_IN
    };

    return langMap[lang.toLowerCase()] || LanguageCode.ZH_CN;
  }
}

module.exports = {
  AsrClient,
};
