const {
  TranscribeStreamingClient,
  StartStreamTranscriptionCommand,
} = require("@aws-sdk/client-transcribe-streaming");

const getTime = () => {
  return new Date().toISOString().replace(/T|Z/g, ' ')
}

// Function to find the start of PCM data in a WAV file
function findPCMDataStart(buffer) {
  // Look for "data" chunk marker
  for (let i = 0; i < buffer.length - 4; i++) {
    if (buffer[i] === 0x64 && buffer[i + 1] === 0x61 && 
        buffer[i + 2] === 0x74 && buffer[i + 3] === 0x61) {
      // Found "data" - PCM data starts 8 bytes later (4 for "data" + 4 for size)
      return i + 8;
    }
  }
  return 44; // Default WAV header size if we can't find data chunk
}

class AsrClient {
  constructor(credentials = null, region = "us-east-1") {
    this.credentials = credentials;
    this.region = region;
    this.client = null;
    this.isStarted = false;
    this.isStarting = false;
    this.globalCallback = () => {};
    this.audioBuffer = [];
    this.headerSkipped = false;
    this.maxChunkSize = 1024 * 4; // 4KB max chunks
    this.currentStream = null;
    this.isTranscribing = false;
  }

  async start() {
    if (this.isStarted || this.isStarting) {
      return true;
    }

    this.isStarting = true;
    
    try {
      this.client = new TranscribeStreamingClient({
        region: this.region,
        credentials: this.credentials,
        maxAttempts: 1,
      });
      
      this.isStarted = true;
      this.isStarting = false;
      console.log(getTime(), 'aws asr started');
      return true;
    } catch (error) {
      this.isStarting = false;
      console.error('AWS ASR start error:', error);
      return false;
    }
  }

  async end() {
    this.isStarted = false;
    this.client = null;
    this.audioBuffer = [];
    this.headerSkipped = false;
    this.isTranscribing = false;
    if (this.currentStream) {
      this.currentStream = null;
    }
  }

  async requestAsr(audioData, callback = () => {}, lang = 'zh') {
    if (!this.isStarted && !this.isStarting) {
      await this.start();
    }

    if (this.isStarting) {
      // Wait for start to complete
      await new Promise(resolve => {
        const checkInterval = setInterval(() => {
          if (!this.isStarting) {
            clearInterval(checkInterval);
            resolve();
          }
        }, 10);
      });
    }

    this.globalCallback = callback;

    // Add audio data to buffer for batch processing
    this.addToBuffer(audioData);

    // Process the buffer if we have enough data or after a delay
    if (!this.isTranscribing) {
      setTimeout(() => this.processBuffer(lang), 100);
    }
  }

  addToBuffer(audioData) {
    let dataToProcess = audioData;

    // Skip WAV header if this is the first chunk
    if (!this.headerSkipped && audioData.length > 44) {
      const pcmDataStart = findPCMDataStart(audioData);
      if (pcmDataStart > 0) {
        console.log(`🔍 WAV header detected, PCM data starts at byte ${pcmDataStart}`);
        dataToProcess = audioData.slice(pcmDataStart);
      }
      this.headerSkipped = true;
    }

    // Add to buffer
    this.audioBuffer.push(dataToProcess);
  }

  async processBuffer(lang = 'zh') {
    if (!this.client || this.isTranscribing || this.audioBuffer.length === 0) {
      return;
    }

    this.isTranscribing = true;

    try {
      // Combine all buffered audio data
      const totalLength = this.audioBuffer.reduce((sum, chunk) => sum + chunk.length, 0);
      const combinedAudio = Buffer.alloc(totalLength);
      let offset = 0;

      for (const chunk of this.audioBuffer) {
        chunk.copy(combinedAudio, offset);
        offset += chunk.length;
      }

      // Create audio stream similar to original demo
      const audioStream = async function* (data, maxChunkSize) {
        let chunkCount = 0;

        for (let i = 0; i < data.length; i += maxChunkSize) {
          const smallChunk = data.slice(i, i + maxChunkSize);
          chunkCount++;

          console.log(`📦 Chunk ${chunkCount}: ${smallChunk.length} bytes PCM data`);
          yield { AudioEvent: { AudioChunk: smallChunk } };

          // Small delay between chunks (like original)
          await new Promise(resolve => setTimeout(resolve, 50));
        }

        console.log(`✅ Audio stream complete: ${chunkCount} chunks, ${data.length} bytes PCM data`);
      };

      const languageCode = this.getLanguageCode(lang);

      // Use exact same structure as original demo
      const params = {
        LanguageCode: languageCode,
        MediaEncoding: "pcm",
        MediaSampleRateHertz: 16000,
        AudioStream: audioStream(combinedAudio, this.maxChunkSize),
      };

      console.log("📡 Sending transcription request...");
      const command = new StartStreamTranscriptionCommand(params);
      const response = await this.client.send(command);

      console.log("🎧 Listening for transcription results...");
      let hasTranscript = false;

      for await (const event of response.TranscriptResultStream) {
        if (event.TranscriptEvent) {
          const results = event.TranscriptEvent.Transcript.Results;

          if (results && results.length > 0) {
            results.forEach((result) => {
              if (result.Alternatives && result.Alternatives.length > 0) {
                const transcript = result.Alternatives[0].Transcript;
                const confidence = result.Alternatives[0].Confidence;
                const isFinal = !result.IsPartial;

                if (transcript && transcript.trim()) {
                  hasTranscript = true;
                  const ret = {
                    isFinal: isFinal,
                    transcript: transcript.trim()
                  };

                  console.log(`📝 ${isFinal ? '[FINAL]' : '[PARTIAL]'} "${transcript}" (confidence: ${confidence || 'N/A'})`);
                  this.globalCallback(ret);
                }
              }
            });
          }
        }
      }

      if (hasTranscript) {
        console.log("🎉 Transcription completed successfully!");
      } else {
        console.log("⚠️  No speech detected in the audio file.");
      }

      // Clear buffer after processing
      this.audioBuffer = [];

    } catch (err) {
      console.error("❌ Error during transcription:");
      console.error(`   ${err.name}: ${err.message}`);
      console.error("Full error:", err);
    } finally {
      this.isTranscribing = false;
    }
  }

  getLanguageCode(lang = 'zh') {
    // Map language codes to AWS supported string values (like in original demo)
    const langMap = {
      'zh': 'zh-CN',
      'zh-cn': 'zh-CN',
      'en': 'en-US',
      'en-us': 'en-US',
      'ja': 'ja-JP',
      'ko': 'ko-KR',
      'fr': 'fr-FR',
      'de': 'de-DE',
      'es': 'es-ES',
      'it': 'it-IT',
      'pt': 'pt-BR',
      'ru': 'ru-RU',
      'ar': 'ar-SA',
      'hi': 'hi-IN'
    };

    return langMap[lang.toLowerCase()] || 'zh-CN';
  }
}

module.exports = {
  AsrClient,
};
